{"Version": 1, "WorkspaceRootPath": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|ViewDataWindow_MSSQL__/._sqlexpress/databasetasks/True/SqlTable/dbo.task_documents.sql||{0058A1F7-65F3-4DB9-B3D0-CA7E64DD73CD}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\durpevco..sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}|"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|ViewDataWindow_MSSQL__/._sqlexpress/databasetasks/True/SqlTable/dbo.tasks.sql||{0058A1F7-65F3-4DB9-B3D0-CA7E64DD73CD}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|ViewDataWindow_MSSQL__/._sqlexpress/databasetasks/True/SqlTable/dbo.archive_documents.sql||{0058A1F7-65F3-4DB9-B3D0-CA7E64DD73CD}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|ViewDataWindow_MSSQL__/._sqlexpress/databasetasks/True/SqlTable/dbo.task_access_users.sql||{0058A1F7-65F3-4DB9-B3D0-CA7E64DD73CD}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\durpevco..sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}|CodeFrame"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\durpevco..sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}|ResultFrame"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 398, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "dbo.task_documents [Data]", "DocumentMoniker": "ViewDataWindow_MSSQL__/._sqlexpress/databasetasks/True/SqlTable/dbo.task_documents.sql", "ToolTip": "dbo.task_documents [Data]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-08-03T21:07:36.888Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "SQLQuery1.sql ", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\durpevco..sql", "ToolTip": "SQLQuery1.sql *", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-08-03T17:33:36.57Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "dbo.archive_documents [Data]", "DocumentMoniker": "ViewDataWindow_MSSQL__/._sqlexpress/databasetasks/True/SqlTable/dbo.archive_documents.sql", "ToolTip": "dbo.archive_documents [Data]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-08-03T17:31:18.311Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "dbo.task_access_users [Data]", "DocumentMoniker": "ViewDataWindow_MSSQL__/._sqlexpress/databasetasks/True/SqlTable/dbo.task_access_users.sql", "ToolTip": "dbo.task_access_users [Data]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-08-03T17:23:34.545Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "dbo.tasks [Data]", "DocumentMoniker": "ViewDataWindow_MSSQL__/._sqlexpress/databasetasks/True/SqlTable/dbo.tasks.sql", "ToolTip": "dbo.tasks [Data]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-08-03T17:22:56.727Z", "EditorCaption": ""}]}]}]}
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/dashboard_models.dart';
import 'resizable_chart_card.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';

/// مدير تخطيط لوحة التحكم المتقدم
class DashboardLayoutManager extends StatefulWidget {
  final List<ChartCardModel> cards;
  final Function(List<ChartCardModel>)? onCardsReordered;
  final Function(ChartCardModel)? onCardUpdated;
  final VoidCallback? onRefresh;
  final Function(String)? onRefreshSingle;

  const DashboardLayoutManager({
    super.key,
    required this.cards,
    this.onCardsReordered,
    this.onCardUpdated,
    this.onRefresh,
    this.onRefreshSingle,
  });

  @override
  State<DashboardLayoutManager> createState() => _DashboardLayoutManagerState();
}

class _DashboardLayoutManagerState extends State<DashboardLayoutManager>
    with TickerProviderStateMixin {
  late List<ChartCardModel> _cards;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  int _columns = 2;
  double _spacing = 16.0;
  bool _isReorderMode = false;
  bool _isEditMode = false;

  // أوضاع العرض (الوضع المخصص معطل مؤقتاً)
  int _viewMode = 0; // 0: شبكي، 1: قائمة، 2: قابل للتخصيص (معطل)

  @override
  void initState() {
    super.initState();
    _cards = List.from(widget.cards);

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();

    // تحميل تفضيلات التخطيط المحفوظة
    _loadLayoutPreferences();
  }

  @override
  void didUpdateWidget(DashboardLayoutManager oldWidget) {
    super.didUpdateWidget(oldWidget);

    // تحديث البطاقات عند تغييرها
    if (oldWidget.cards != widget.cards) {
      setState(() {
        _cards = List.from(widget.cards);
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// تحميل تفضيلات التخطيط المحفوظة
  Future<void> _loadLayoutPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      setState(() {
        _columns = prefs.getInt('dashboard_layout_columns') ?? 2;
        _spacing = prefs.getDouble('dashboard_layout_spacing') ?? 16.0;

        // منع تحميل الوضع المخصص (2) - إعادة تعيين للوضع الشبكي
        final savedViewMode = prefs.getInt('dashboard_layout_view_mode') ?? 0;
        _viewMode = savedViewMode == 2 ? 0 : savedViewMode; // إذا كان مخصص، تحويل لشبكي
      });

      debugPrint('✅ تم تحميل تفضيلات التخطيط:');
      debugPrint('   الأعمدة: $_columns');
      debugPrint('   التباعد: $_spacing');
      debugPrint('   وضع العرض: $_viewMode (0:شبكي، 1:قائمة، 2:مخصص-معطل)');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل تفضيلات التخطيط: $e');
    }
  }

  /// حفظ تفضيلات التخطيط
  Future<void> _saveLayoutPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setInt('dashboard_layout_columns', _columns);
      await prefs.setDouble('dashboard_layout_spacing', _spacing);
      await prefs.setInt('dashboard_layout_view_mode', _viewMode);

      debugPrint('✅ تم حفظ تفضيلات التخطيط:');
      debugPrint('   الأعمدة: $_columns');
      debugPrint('   التباعد: $_spacing');
      debugPrint('   وضع العرض: $_viewMode (0:شبكي، 1:قائمة، 2:مخصص-معطل)');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ تفضيلات التخطيط: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildLayoutControls(),
        const SizedBox(height: 16),
        Expanded(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: _buildCurrentLayout(),
          ),
        ),
      ],
    );
  }

  /// بناء عناصر التحكم في التخطيط
  Widget _buildLayoutControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
        boxShadow: [
          BoxShadow(
            color: AppColors.getShadowColor(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // تبديل وضع العرض
          ToggleButtons(
            isSelected: [
              _viewMode == 0, // شبكي
              _viewMode == 1, // قائمة
              // _viewMode == 2, // مخصص (معطل مؤقتاً)
              false, // الوضع المخصص معطل
            ],
            onPressed: (index) {
              // منع الوصول للوضع المخصص (index == 2)
              if (index == 2) return; // الوضع المخصص معطل مؤقتاً

              setState(() {
                _viewMode = index;
              });
              HapticFeedback.selectionClick();
              _saveLayoutPreferences();
            },
            borderRadius: BorderRadius.circular(8),
            children: [
              const Tooltip(
                message: 'عرض شبكي',
                child: Icon(Icons.grid_view),
              ),
              const Tooltip(
                message: 'عرض قائمة',
                child: Icon(Icons.view_list),
              ),
              // الوضع المخصص معطل مؤقتاً
              Tooltip(
                message: 'أحجام مخصصة (معطل مؤقتاً)',
                child: Icon(Icons.dashboard_customize, color: AppColors.disabled),
              ),
            ],
          ),
          
          const SizedBox(width: 16),
          
          // عدد الأعمدة (للعرض الشبكي فقط)
          if (_viewMode == 0) ...[
            Text(
              'الأعمدة:',
              style: AppStyles.bodyMedium,
            ),
            const SizedBox(width: 8),
            DropdownButton<int>(
              value: _columns,
              items: [1, 2, 3, 4].map((columns) {
                return DropdownMenuItem<int>(
                  value: columns,
                  child: Text('$columns',style: TextStyle(color: AppColors.textPrimary),),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _columns = value;
                  });
                  HapticFeedback.selectionClick();
                  _saveLayoutPreferences();
                }
              },
            ),
            
            const SizedBox(width: 16),
          ],
          
          // تباعد البطاقات
          Text(
            'التباعد:',
            style: AppStyles.bodyMedium,
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 100,
            child: Slider(
              value: _spacing,
              min: 8.0,
              max: 32.0,
              divisions: 6,
              onChanged: (value) {
                setState(() {
                  _spacing = value;
                });
              },
              onChangeEnd: (value) {
                HapticFeedback.lightImpact();
                _saveLayoutPreferences();
              },
            ),
          ),
          
          // رسالة توضيحية للوضع المخصص (معطل مؤقتاً)
          /* if (_viewMode == 2) ...[
            const SizedBox(width: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 16,
                    color: AppColors.primary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'استخدم وضع التحرير لتغيير الأحجام',
                    style: AppStyles.bodySmall.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ], */

          const Spacer(),
          
          // وضع التحرير
          Tooltip(
            message: _isEditMode ? 'إنهاء التحرير' : 'تحرير البطاقات',
            child: IconButton(
              icon: Icon(
                _isEditMode ? Icons.done : Icons.edit,
                color: _isEditMode ? AppColors.success : AppColors.primary,
              ),
              onPressed: () {
                setState(() {
                  _isEditMode = !_isEditMode;
                  if (_isEditMode) {
                    _isReorderMode = false; // إيقاف وضع إعادة الترتيب
                  }
                });
                HapticFeedback.mediumImpact();
              },
            ),
          ),

          // وضع إعادة الترتيب
          Tooltip(
            message: _isReorderMode ? 'إنهاء إعادة الترتيب' : 'إعادة ترتيب البطاقات',
            child: IconButton(
              icon: Icon(
                _isReorderMode ? Icons.done : Icons.reorder,
                color: _isReorderMode ? AppColors.success : AppColors.primary,
              ),
              onPressed: () {
                // تأخير بسيط لضمان الاستقرار
                Future.microtask(() {
                  setState(() {
                    _isReorderMode = !_isReorderMode;
                    if (_isReorderMode) {
                      _isEditMode = false; // إيقاف وضع التحرير
                    }
                  });
                });
                HapticFeedback.mediumImpact();
              },
            ),
          ),
          
          // تحديث البيانات
          Tooltip(
            message: 'تحديث البيانات',
            child: IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: widget.onRefresh,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء التخطيط الحالي حسب الوضع المختار
  Widget _buildCurrentLayout() {
    // الحفاظ على الترتيب والمسافات عند تغيير الأوضاع
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 200),
      child: _buildLayoutByMode(),
    );
  }

  /// بناء التخطيط حسب الوضع مع الحفاظ على الاستقرار
  Widget _buildLayoutByMode() {
    switch (_viewMode) {
      case 0: // شبكي
        return _buildGridLayout();
      case 1: // قائمة
        return _buildListLayout();
      // case 2: // مخصص (معطل مؤقتاً)
      //   return _buildCustomLayout();
      default:
        return _buildGridLayout();
    }
  }

  /// بناء التخطيط الشبكي مع الحفاظ على الاستقرار
  Widget _buildGridLayout() {
    if (_isReorderMode) {
      return _buildReorderableGridStable();
    }

    return GridView.builder(
      key: const ValueKey('normal_grid'),
      padding: EdgeInsets.all(_spacing),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _columns,
        crossAxisSpacing: _spacing,
        mainAxisSpacing: _spacing,
        childAspectRatio: _calculateAspectRatio(),
      ),
      itemCount: _cards.length,
      itemBuilder: (context, index) {
        return _buildCardWithAnimation(_cards[index], index);
      },
    );
  }

  /// بناء الشبكة القابلة لإعادة الترتيب مع الحفاظ على نفس التخطيط
  Widget _buildReorderableGridStable() {
    return GridView.builder(
      key: const ValueKey('reorderable_grid'),
      padding: EdgeInsets.all(_spacing),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _columns,
        crossAxisSpacing: _spacing,
        mainAxisSpacing: _spacing,
        childAspectRatio: _calculateAspectRatio(),
      ),
      itemCount: _cards.length,
      itemBuilder: (context, index) {
        final card = _cards[index];

        return DragTarget<int>(
          onWillAcceptWithDetails: (details) {
            return details.data != index;
          },
          onAcceptWithDetails: (details) {
            _handleReorder(details.data, index);
          },
          builder: (context, candidateData, rejectedData) {
            final isHovering = candidateData.isNotEmpty;

            return Container(
              // إزالة AnimatedContainer لتجنب تأثيرات الحجم
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: isHovering
                    ? Border.all(color: AppColors.success, width: 2)
                    : Border.all(color: AppColors.transparent, width: 2), // حدود شفافة للحفاظ على الحجم
                color: isHovering
                    ? AppColors.success.withValues(alpha: 0.1)
                    : null,
              ),
              child: Stack(
                fit: StackFit.expand, // للحفاظ على الحجم الكامل
                children: [
                  // البطاقة الأساسية
                  Positioned.fill(
                    child: _buildCardWithAnimation(card, index),
                  ),
                  // مقبض السحب
                  Positioned(
                    top: 4,
                    right: 4,
                    child: _buildDragHandle(index),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  /// بناء التخطيط القائمة مع الحفاظ على الاستقرار
  Widget _buildListLayout() {
    final key = ValueKey('list_layout_$_spacing');

    return ListView.separated(
      key: key,
      padding: EdgeInsets.all(_spacing),
      itemCount: _cards.length,
      separatorBuilder: (context, index) => SizedBox(height: _spacing),
      itemBuilder: (context, index) {
        return SizedBox(
          height: 300,
          child: _buildCardWithAnimation(_cards[index], index),
        );
      },
    );
  }



  /// بناء مقبض السحب المحسن والمضغوط
  Widget _buildDragHandle(int index) {
    return Draggable<int>(
      data: index,
      onDragStarted: () {
        debugPrint('🚀 بدء سحب البطاقة من المقبض: index=$index');
        HapticFeedback.lightImpact();
      },
      onDragEnd: (details) {
        debugPrint('🏁 انتهاء سحب البطاقة: index=$index, wasAccepted=${details.wasAccepted}');
        if (details.wasAccepted) {
          HapticFeedback.mediumImpact();
        }
      },
      feedback: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: AppColors.getShadowColor(0.3),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            Icons.drag_indicator,
            color: AppColors.white,
            size: 16,
          ),
        ),
      ),
      childWhenDragging: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: AppColors.primary.withValues(alpha: 0.5),
            width: 1,
          ),
        ),
        child: Icon(
          Icons.drag_indicator,
          color: AppColors.primary.withValues(alpha: 0.7),
          size: 12,
        ),
      ),
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(6),
          boxShadow: [
            BoxShadow(
              color: AppColors.getShadowColor(0.2),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Icon(
          Icons.drag_indicator,
          color: AppColors.white,
          size: 12,
        ),
      ),
    );
  }

  /// بناء البطاقة مع الرسوم المتحركة
  Widget _buildCardWithAnimation(ChartCardModel card, int index) {
    // تجنب الرسوم المتحركة في وضع إعادة الترتيب لتجنب التداخل
    if (_isReorderMode) {
      return ResizableChartCard(
        key: ValueKey('card_${card.id}'),
        card: card.copyWith(isResizable: false), // إيقاف تغيير الحجم في وضع الترتيب
        onCardUpdated: widget.onCardUpdated ?? (updatedCard) {
          setState(() {
            final cardIndex = _cards.indexWhere((c) => c.id == updatedCard.id);
            if (cardIndex != -1) {
              _cards[cardIndex] = updatedCard;
            }
          });
        },
        onRefresh: widget.onRefresh,
        onRefreshSingle: widget.onRefreshSingle,
        onChartTypeChanged: (newType) {
          setState(() {
            final cardIndex = _cards.indexWhere((c) => c.id == card.id);
            if (cardIndex != -1) {
              _cards[cardIndex] = _cards[cardIndex].copyWith(chartType: newType);
            }
          });
        },
        onSizeChanged: (newSize) {
          debugPrint('📏 تحديث حجم البطاقة ${card.id}: ${newSize.width}x${newSize.height}');
          setState(() {
            final cardIndex = _cards.indexWhere((c) => c.id == card.id);
            if (cardIndex != -1) {
              _cards[cardIndex] = _cards[cardIndex].copyWith(size: newSize);
              debugPrint('✅ تم تحديث البطاقة في القائمة المحلية');
            } else {
              debugPrint('❌ لم يتم العثور على البطاقة في القائمة');
            }
          });
        },
      );
    }

    // الرسوم المتحركة العادية في الأوضاع الأخرى
    return AnimatedContainer(
      duration: Duration(milliseconds: 300 + (index * 50)),
      curve: Curves.easeOutBack,
      child: ResizableChartCard(
        key: ValueKey('animated_card_${card.id}'),
        card: card.copyWith(isResizable: _isEditMode),
        onCardUpdated: widget.onCardUpdated ?? (updatedCard) {
          setState(() {
            final cardIndex = _cards.indexWhere((c) => c.id == updatedCard.id);
            if (cardIndex != -1) {
              _cards[cardIndex] = updatedCard;
            }
          });
        },
        onRefresh: widget.onRefresh,
        onRefreshSingle: widget.onRefreshSingle,
        onChartTypeChanged: (newType) {
          setState(() {
            final cardIndex = _cards.indexWhere((c) => c.id == card.id);
            if (cardIndex != -1) {
              _cards[cardIndex] = _cards[cardIndex].copyWith(chartType: newType);
            }
          });
        },
        onSizeChanged: (newSize) {
          debugPrint('📏 تحديث حجم البطاقة ${card.id}: ${newSize.width}x${newSize.height}');
          setState(() {
            final cardIndex = _cards.indexWhere((c) => c.id == card.id);
            if (cardIndex != -1) {
              _cards[cardIndex] = _cards[cardIndex].copyWith(size: newSize);
              debugPrint('✅ تم تحديث البطاقة في القائمة المحلية');
            } else {
              debugPrint('❌ لم يتم العثور على البطاقة في القائمة');
            }
          });
        },
      ),
    );
  }

  /// بناء التخطيط المخصص (أحجام حرة) - معطل مؤقتاً
  /* Widget _buildCustomLayout() {
    if (_isReorderMode) {
      return _buildCustomReorderableLayout();
    }

    return SingleChildScrollView(
      key: const ValueKey('custom_layout'),
      padding: EdgeInsets.all(_spacing),
      child: Wrap(
        spacing: _spacing,
        runSpacing: _spacing,
        children: _cards.asMap().entries.map((entry) {
          final index = entry.key;
          final card = entry.value;

          // في الوضع المخصص، نترك البطاقة تحدد حجمها بنفسها
          return _buildCardWithAnimation(card, index);
        }).toList(),
      ),
    );
  } */

  /// بناء التخطيط المخصص القابل لإعادة الترتيب - معطل مؤقتاً
  /* Widget _buildCustomReorderableLayout() {
    return SingleChildScrollView(
      key: const ValueKey('custom_reorderable'),
      padding: EdgeInsets.all(_spacing),
      child: ReorderableWrap(
        spacing: _spacing,
        runSpacing: _spacing,
        onReorder: _handleReorder,
        children: _cards.asMap().entries.map((entry) {
          final index = entry.key;
          final card = entry.value;

          return SizedBox(
            key: ValueKey('custom_card_${card.id}_$index'),
            width: card.size.width,
            height: card.size.height,
            child: Stack(
              children: [
                _buildCardWithAnimation(card, index),
                // مقبض السحب
                Positioned(
                  top: 8,
                  right: 8,
                  child: _buildDragHandle(index),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  } */

  /// حساب نسبة العرض إلى الارتفاع
  double _calculateAspectRatio() {
    switch (_columns) {
      case 1:
        return 2.0;
      case 2:
        return 1.3;
      case 3:
        return 1.1;
      case 4:
        return 0.9;
      default:
        return 1.2;
    }
  }





  /// معالجة إعادة الترتيب
  void _handleReorder(int oldIndex, int newIndex) {
    debugPrint('🔄 بدء إعادة الترتيب:');
    debugPrint('   oldIndex: $oldIndex');
    debugPrint('   newIndex (أصلي): $newIndex');

    // التحقق من صحة المؤشرات
    if (oldIndex < 0 || oldIndex >= _cards.length ||
        newIndex < 0 || newIndex >= _cards.length ||
        oldIndex == newIndex) {
      debugPrint('❌ مؤشرات غير صحيحة، إلغاء العملية');
      return;
    }

    setState(() {
      // خوارزمية محسنة لإعادة الترتيب
      final card = _cards.removeAt(oldIndex);
      debugPrint('   البطاقة المزالة: ${card.id}');

      // تعديل المؤشر بناءً على الاتجاه
      int adjustedIndex = newIndex;
      if (newIndex > oldIndex) {
        adjustedIndex = newIndex - 1;
        debugPrint('   تعديل المؤشر للسحب لليمين: $adjustedIndex');
      } else {
        debugPrint('   لا حاجة لتعديل المؤشر للسحب لليسار: $adjustedIndex');
      }

      // التأكد من أن المؤشر المعدل صحيح
      adjustedIndex = adjustedIndex.clamp(0, _cards.length);

      debugPrint('   إدراج البطاقة في الموقع: $adjustedIndex');
      _cards.insert(adjustedIndex, card);

      debugPrint('   الترتيب الجديد: ${_cards.map((c) => c.id).toList()}');
    });

    widget.onCardsReordered?.call(_cards);
    HapticFeedback.mediumImpact();
    debugPrint('✅ تم إكمال إعادة الترتيب');
  }
}

/// Widget مخصص لإعادة ترتيب العناصر في شكل Wrap
class ReorderableWrap extends StatefulWidget {
  final List<Widget> children;
  final Function(int, int) onReorder;
  final double spacing;
  final double runSpacing;

  const ReorderableWrap({
    super.key,
    required this.children,
    required this.onReorder,
    this.spacing = 0.0,
    this.runSpacing = 0.0,
  });

  @override
  State<ReorderableWrap> createState() => _ReorderableWrapState();
}

class _ReorderableWrapState extends State<ReorderableWrap> {
  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: widget.spacing,
      runSpacing: widget.runSpacing,
      children: widget.children.asMap().entries.map((entry) {
        final index = entry.key;
        final child = entry.value;
        
        return DragTarget<int>(
            onWillAcceptWithDetails: (details) {
              final willAccept = details.data != index;
              debugPrint('🎯 DragTarget[$index]: willAccept=$willAccept, dragData=${details.data}');
              return willAccept;
            },
            onAcceptWithDetails: (details) {
              debugPrint('✅ DragTarget[$index]: قبول السحب من ${details.data} إلى $index');
              widget.onReorder(details.data, index);
            },
            builder: (context, candidateData, rejectedData) {
              final isHovering = candidateData.isNotEmpty;
              if (isHovering) {
                debugPrint('🎯 التحويم فوق البطاقة: index=$index, dragData=${candidateData.first}');
              }
              return AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: isHovering
                      ? Border.all(
                          color: AppColors.success,
                          width: 3,
                        )
                      : null,
                  // إضافة خلفية خفيفة عند التحويم
                  color: isHovering
                      ? AppColors.success.withValues(alpha: 0.1)
                      : null,
                ),
                child: child,
              );
            },
          );
      }).toList(),
    );
  }
}
